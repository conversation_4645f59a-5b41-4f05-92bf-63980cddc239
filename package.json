{"name": "ruet-materials-club-website", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "generate:types": "PAYLOAD_CONFIG_PATH=src/payload.config.ts payload generate:types", "prepare": "husky"}, "dependencies": {"@payloadcms/db-vercel-postgres": "^3.55.1", "@payloadcms/next": "^3.55.1", "@payloadcms/richtext-lexical": "^3.55.1", "@payloadcms/storage-vercel-blob": "^3.55.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-slot": "^1.2.3", "@react-hook/throttle": "^2.2.0", "@react-three/drei": "^10.7.6", "@react-three/fiber": "^9.3.0", "@tailwindcss/postcss": "^4.1.13", "@types/three": "^0.180.0", "@vercel/blob": "^2.0.0", "change-case": "^5.4.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "disable-scroll": "^0.6.0", "graphql": "^16.11.0", "hamburger-react": "^2.5.2", "lucide-react": "^0.544.0", "motion": "^12.23.13", "next": "^15.5.3", "package-manager-detector": "^1.3.0", "payload": "^3.55.1", "prettier-plugin-organize-imports": "^4.2.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-icons": "^5.5.0", "react-swipeable": "^7.0.2", "sharp": "^0.34.3", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "three": "^0.180.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.19.15", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "eslint": "^9.35.0", "eslint-config-next": "15.1.6", "eslint-config-prettier": "^10.1.8", "eslint-plugin-react-compiler": "^19.1.0-rc.2", "husky": "^9.1.7", "lint-staged": "^16.1.6", "postcss": "^8.5.6", "prettier": "3.4.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.13", "typescript": "^5.9.2"}, "packageManager": "bun@1.2.22"}