import { parse } from "csv-parse/sync";
import { promises as fs } from "fs"; // 'fs/promises' not available in node 12

const content = await fs.readFile(`data.csv`);
// Parse the CSV content
const records = parse(content, { bom: true });
// Validate the records
const d = records.map((record) => record.slice(0, 3).map((x) => x.trim()));

d.map(async (record) => {
  const exists = await Bun.file(`${record[0]}.png`).exists();
  if (!exists) {
    console.log(`Image not found for ${record[0]}`);
  }
});
Bun.file("data.json").write(JSON.stringify(d, null, 2))
exit(0);
// import config from "@payload-config";
// import { getPayload } from "payload";
// import path from "path";
import { exit } from "process";

const seed = async () => {
  // Get a local copy of Payload by passing your config
  const payload = await getPayload({ config });

  for (const record of d) {
    const localFilePath = path.resolve(__dirname, `${record[0]}.png`);

    const photo = await payload.create({
      collection: "media",
      data: {
        alt: record[0],
      },
      filePath: localFilePath,
    });
    const member = await payload.create({
      collection: "members",
      data: {
        fullName: record[0],
        photo: "",
        email: record[2],
        position: record[1],
        orderWithInGroup: 0,
        groupOrder: 3,
        committee: 1,
      },
    });
  }
};

await seed();
