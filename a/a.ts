import config from "@payload-config";
import path from "path";
import { getPayload } from "payload";
import { data } from "./data";

const seed = async () => {
  // Get a local copy of Payload by passing your config
  const payload = await getPayload({ config });

  for (const record of data) {
    const localFilePath = path.resolve(__dirname, `a/${record[0]}.webp`);

    const photo = await payload.create({
      collection: "media",
      data: {
        alt: record[0],
      },
      filePath: localFilePath,
    });

    const member = await payload.create({
      collection: "members",
      data: {
        fullName: record[0],
        photo: photo,
        email: record[2],
        position: record[1],
        orderWithInGroup: 0,
        groupOrder: record[3],
        committee: 1,
      },
    });
    console.log(member.id, member.fullName);
  }
};

await seed();
